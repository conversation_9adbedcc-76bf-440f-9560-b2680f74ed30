package resolvers

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// TestConvertTierBenefitToGQLWithUserID_WithReferrer tests cashback percentage with referral bonus
func TestConvertTierBenefitToGQLWithUserID_WithReferrer(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	// Create a test tier benefit with 10% cashback
	tierBenefit := &model.TierBenefit{
		ID:                 1,
		TierLevel:          1,
		TierName:           "Bronze",
		MinPoints:          0,
		CashbackPercentage: decimal.NewFromFloat(0.10), // 10% base cashback
		NetFee:             decimal.NewFromFloat(0.001),
		IsActive:           true,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Create test user with referrer
	userID := uuid.New()
	referrerID := uuid.New()

	// Create test users in database
	invitationCode1 := "TEST123"
	invitationCode2 := "REF456"
	testUser := &model.User{
		ID:             userID,
		InvitationCode: &invitationCode1,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	testReferrer := &model.User{
		ID:             referrerID,
		InvitationCode: &invitationCode2,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Insert test data
	global.GVA_DB.Create(testReferrer)
	global.GVA_DB.Create(testUser)

	// Create referral relationship
	referral := &model.Referral{
		UserID:     userID,
		ReferrerID: &referrerID,
		Depth:      1,
		CreatedAt:  time.Now(),
	}
	global.GVA_DB.Create(referral)

	// Test conversion with userID (should include 5% bonus)
	gqlBenefit := convertTierBenefitToGQLWithUserID(tierBenefit, &userID)

	// Verify the result
	assert.NotNil(t, gqlBenefit)
	assert.Equal(t, "1", gqlBenefit.ID)
	assert.Equal(t, 1, gqlBenefit.TierLevel)
	assert.Equal(t, "Bronze", gqlBenefit.TierName)

	// Should be 15% (10% base + 5% referral bonus) = 15.0
	assert.InDelta(t, 15.0, gqlBenefit.CashbackPercentage, 0.001)
}

// TestConvertTierBenefitToGQLWithUserID_WithoutReferrer tests cashback percentage without referral bonus
func TestConvertTierBenefitToGQLWithUserID_WithoutReferrer(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	// Create a test tier benefit with 10% cashback
	tierBenefit := &model.TierBenefit{
		ID:                 1,
		TierLevel:          1,
		TierName:           "Bronze",
		MinPoints:          0,
		CashbackPercentage: decimal.NewFromFloat(0.10), // 10% base cashback
		NetFee:             decimal.NewFromFloat(0.001),
		IsActive:           true,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Create test user without referrer
	userID := uuid.New()

	invitationCode := "TEST789"
	testUser := &model.User{
		ID:             userID,
		InvitationCode: &invitationCode,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Insert test data
	global.GVA_DB.Create(testUser)

	// Test conversion with userID (should NOT include bonus)
	gqlBenefit := convertTierBenefitToGQLWithUserID(tierBenefit, &userID)

	// Verify the result
	assert.NotNil(t, gqlBenefit)
	assert.Equal(t, "1", gqlBenefit.ID)
	assert.Equal(t, 1, gqlBenefit.TierLevel)
	assert.Equal(t, "Bronze", gqlBenefit.TierName)

	// Should be 10% (10% base, no referral bonus) = 10.0
	assert.Equal(t, 10.0, gqlBenefit.CashbackPercentage)
}

// TestConvertTierBenefitToGQL_BackwardCompatibility tests backward compatibility without userID
func TestConvertTierBenefitToGQL_BackwardCompatibility(t *testing.T) {
	// Create a test tier benefit with 10% cashback
	tierBenefit := &model.TierBenefit{
		ID:                 1,
		TierLevel:          1,
		TierName:           "Bronze",
		MinPoints:          0,
		CashbackPercentage: decimal.NewFromFloat(0.10), // 10% base cashback
		NetFee:             decimal.NewFromFloat(0.001),
		IsActive:           true,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Test conversion without userID (backward compatibility)
	gqlBenefit := convertTierBenefitToGQL(tierBenefit)

	// Verify the result
	assert.NotNil(t, gqlBenefit)
	assert.Equal(t, "1", gqlBenefit.ID)
	assert.Equal(t, 1, gqlBenefit.TierLevel)
	assert.Equal(t, "Bronze", gqlBenefit.TierName)

	// Should be 10% (10% base, no referral bonus since no userID provided) = 10.0
	assert.Equal(t, 10.0, gqlBenefit.CashbackPercentage)
}
