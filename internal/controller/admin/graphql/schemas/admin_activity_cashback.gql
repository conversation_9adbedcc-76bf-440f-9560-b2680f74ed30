# Admin Activity Cashback Schema - Types and Inputs

# Multilingual Name Type
type MultilingualName {
  en: String!
  zh: String
  ja: String
  hi: String
  hk: String
  vi: String
}

input MultilingualNameInput {
  en: String!
  zh: String
  ja: String
  hi: String
  hk: String
  vi: String
}

# Task Types
type ActivityTask {
  id: ID!
  categoryId: ID!
  category: TaskCategory!
  name: MultilingualName!
  description: String

  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  startDate: Int64
  endDate: Int64
  sortOrder: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}
type ALlTaskCategoriesResponse {
  data: [TaskCategory!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}

type TaskCategory {
  id: ID!
  name: TaskCategoryName!
  displayName: String!
  description: String
  icon: String
  isActive: Boolean!
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
}

# Enums
enum TaskCategoryName {
  DAILY
  COMMUNITY
  TRADING
}



enum TaskFrequency {
  DAILY
  PROGRESSIVE
  ONE_TIME
  MANUAL
  UNLIMITED
}

enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CHECK_MARKET_TRENDS

  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL
  SHARE_EARNINGS_CHART

  # Trading Tasks
  TRADING_POINTS
  ACCUMULATED_TRADING_5K
  ACCUMULATED_TRADING_10K
  ACCUMULATED_TRADING_50K
  ACCUMULATED_TRADING_100K
  ACCUMULATED_TRADING_500K
}

# User Tier Types
type UserTierInfo {
  userId: ID!
  email: String
  currentTier: TierBenefit
  totalPoints: Int!
  availableCashback: Float!
  totalCashbackClaimed: Float!
  nextTier: TierBenefit
  pointsToNextTier: Int
  createdAt: Time!
  lastActivityAt: Time
}

type ALlTierBenefitsResponse {
  data: [TierBenefit!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}
type TierBenefit {
  id: ID!
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  netFee: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type TierBenefitResponse {
  success: Boolean!
  message: String!
  data: TierBenefit
}

# Input Types
input CreateTaskInput {
  categoryId: ID!
  name: MultilingualNameInput!
  description: String

  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input UpdateTaskInput {
  id: ID!
  categoryId: ID
  name: MultilingualNameInput
  description: String

  frequency: TaskFrequency
  taskIdentifier: TaskIdentifier
  points: Int
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  startDate: Int64
  endDate: Int64
  sortOrder: Int
  isActive: Boolean
}

# Input for consecutive check-in milestone configuration
input ConsecutiveCheckinMilestoneInput {
  days: Int!     # Number of consecutive days required
  points: Int!   # Points awarded when milestone is reached
  name: MultilingualNameInput # Multilingual name for the milestone
}

# Input for creating consecutive check-in task with configurable milestones
input CreateConsecutiveCheckinTaskInput {
  categoryId: ID!
  name: MultilingualNameInput!
  description: String
  milestones: [ConsecutiveCheckinMilestoneInput!]! # Array of milestones (e.g., [3,7,30] days)
  actionTarget: String
  verificationMethod: String
  taskIcon: String
  buttonText: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input CreateTaskCategoryInput {
  name: TaskCategoryName!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int
}

input UpdateTaskCategoryInput {
  id: ID!
  name: TaskCategoryName
  displayName: String
  description: String
  icon: String
  isActive: Boolean
  sortOrder: Int
}
input AllTaskCategoriesInput {
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}
input CreateTierBenefitInput {
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  netFee: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  tierName: String
  minPoints: Int
  cashbackPercentage: Float
  netFee: Float
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean
}
input AllTierBenefitsInput {
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}
# Admin input types
input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}

# Admin response types
type AdminTaskCompletionStatsResponse {
  success: Boolean!
  message: String!
  data: AdminTaskCompletionStats
}

type AdminTaskCompletionStats {
  taskCompletions: [TaskCompletionStat!]!
  startDate: Time!
  endDate: Time!
  totalTasks: Int!
}

type TaskCompletionStat {
  taskName: String!
  completionCount: Int!
}

type AdminUserActivityStatsResponse {
  success: Boolean!
  message: String!
  data: AdminUserActivityStats
}

type AdminUserActivityStats {
  dailyCompletions: [DailyCompletionStat!]!
  startDate: Time!
  endDate: Time!
}

type DailyCompletionStat {
  date: String!
  completionCount: Int!
}

type AdminTierDistributionResponse {
  success: Boolean!
  message: String!
  data: [TierDistributionStat!]!
}

type TierDistributionStat {
  tierLevel: Int!
  userCount: Int!
}
